package com.amobear.themepack.presentation.home.screens.profile

import androidx.compose.runtime.Stable
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.WallpaperPack
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

/**
 * Enhanced Profile screen state with unlock tracking
 */
@Stable
data class ProfileState(
    val selectedTab: ProfileTab = ProfileTab.THEME,
    val coinBalance: Int = 0, // Will be loaded from SharedPreferences
    val unlockedThemes: ImmutableList<Theme> = persistentListOf(),
    val unlockedWallpaperPacks: ImmutableList<WallpaperPack> = persistentListOf(),
    val unlockedWidgetPacks: ImmutableList<WidgetPack> = persistentListOf(),
    val unlockedIconPacks: ImmutableList<IconPack> = persistentListOf(),
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Unified content item for display in profile
 */
@Stable
data class UnlockedContentItem(
    val id: String,
    val title: String,
    val description: String,
    val previewImage: String,
    val isDownloaded: Boolean,
    val localPath: String?
)

/**
 * Profile tabs enum
 */
enum class ProfileTab(val title: String) {
    THEME("Theme"),
    WALLPAPER("Wallpaper"), 
    WIDGET("Widget")
}

/**
 * Profile events for navigation and side effects
 */
sealed interface ProfileEvent {
    data class ShowError(val message: String) : ProfileEvent
    data class NavigateToPurchase(val contentId: String) : ProfileEvent
}
