package com.amobear.themepack.presentation.home.screens.profile

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination

data object ProfileDestination : ThemeNavigationDestination {
    override val route: String = "profile_route"
    override val destination: String = "profile_destination"
}

fun NavGraphBuilder.profileGraph(
    onNavigateBack: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    onNavigateToPurchase: () -> Unit = {}
) {
    composable(route = ProfileDestination.route) {
        com.amobear.themepack.presentation.home.screens.ProfileRoute(
            onNavigateBack = onNavigateBack,
            onNavigateToSettings = onNavigateToSettings,
            onNavigateToPurchase = onNavigateToPurchase
        )
    }
}
